# CoPywork Development Dependencies
#
# Install with: pip install -r requirements-dev.txt

# Include base requirements
-r requirements.txt

# Development tools
pytest>=7.0.0                # Testing framework
pytest-cov>=4.0.0           # Coverage reporting
black>=22.0.0               # Code formatting
flake8>=4.0.0               # Code linting
mypy>=0.950                 # Type checking
isort>=5.10.0               # Import sorting

# Documentation tools
sphinx>=4.0.0               # Documentation generation
sphinx-rtd-theme>=1.0.0     # Read the Docs theme

# Development utilities
pre-commit>=2.15.0          # Git hooks for code quality
bandit>=1.7.0               # Security linting
