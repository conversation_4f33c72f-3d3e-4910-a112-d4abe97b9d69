"""
Syntax highlighter for CoPywork using Pygments and VSCode themes
"""
import tkinter as tk
import tkinter.font as tkfont
from typing import Dict, List, Optional, Tuple
import re

try:
    from pygments import highlight
    from pygments.lexers import <PERSON><PERSON><PERSON><PERSON>, get_lexer_by_name
    from pygments.token import Token
    from pygments.formatters import get_formatter_by_name
    PYGMENTS_AVAILABLE = True
except ImportError:
    PYGMENTS_AVAILABLE = False
    # Create a dummy Token class for when Pygments is not available
    class DummyToken:
        pass

    class Token:
        Comment = DummyToken()
        Keyword = DummyToken()
        String = DummyToken()
        Number = DummyToken()
        Name = DummyToken()
        Operator = DummyToken()
        Punctuation = DummyToken()
        Literal = DummyToken()

    # Add nested attributes
    Token.Comment.Single = DummyToken()
    Token.Comment.Multiline = DummyToken()
    Token.Keyword.Constant = DummyToken()
    Token.Keyword.Declaration = DummyToken()
    Token.Keyword.Namespace = DummyToken()
    Token.Keyword.Reserved = DummyToken()
    Token.String.Double = DummyToken()
    Token.String.Single = DummyToken()
    Token.Number.Integer = DummyToken()
    Token.Number.Float = DummyToken()
    Token.Name.Function = DummyToken()
    Token.Name.Class = DummyToken()
    Token.Name.Builtin = DummyToken()
    Token.Name.Variable = DummyToken()
    Token.Name.Decorator = DummyToken()
    Token.Literal.String = DummyToken()
    Token.Literal.String.Doc = DummyToken()

from theme_loader import ThemeLoader


class SyntaxHighlighter:
    """Handles syntax highlighting for the text widget"""
    
    def __init__(self, text_widget: tk.Text, theme_loader: ThemeLoader):
        self.text_widget = text_widget
        self.theme_loader = theme_loader
        self.lexer = None
        self.configured_tags = set()
        
        # Initialize lexer if Pygments is available
        if PYGMENTS_AVAILABLE:
            self.lexer = PythonLexer()
        
        # Configure base font
        self.base_font = tkfont.Font(family='Fira Code', size=12)
        self.bold_font = tkfont.Font(family='Fira Code', size=12, weight='bold')
        self.italic_font = tkfont.Font(family='Fira Code', size=12, slant='italic')
        self.bold_italic_font = tkfont.Font(family='Fira Code', size=12, weight='bold', slant='italic')
        
        # Map Pygments tokens to VSCode scopes
        self.token_scope_map = {
            Token.Comment: "comment",
            Token.Comment.Single: "comment",
            Token.Comment.Multiline: "comment",
            Token.Keyword: "keyword",
            Token.Keyword.Constant: "keyword.control",
            Token.Keyword.Declaration: "keyword.control",
            Token.Keyword.Namespace: "keyword.control.import",
            Token.Keyword.Reserved: "keyword.control",
            Token.String: "string",
            Token.String.Double: "string",
            Token.String.Single: "string",
            Token.Number: "constant.numeric",
            Token.Number.Integer: "constant.numeric",
            Token.Number.Float: "constant.numeric",
            Token.Name.Function: "entity.name.function",
            Token.Name.Class: "entity.name.class",
            Token.Name.Builtin: "support.function",
            Token.Name.Variable: "variable",
            Token.Operator: "keyword.operator",
            Token.Punctuation: "punctuation",
            Token.Name.Decorator: "entity.name.function.decorator",
            Token.Literal.String.Doc: "comment",
        }
    
    def is_python_file(self, file_path: str) -> bool:
        """Check if file should have Python syntax highlighting"""
        if not file_path:
            return False
        return file_path.lower().endswith(('.py', '.py.cw'))
    
    def configure_tag(self, tag_name: str, scope: str):
        """Configure a text widget tag based on theme scope"""
        if tag_name in self.configured_tags:
            return
            
        foreground = self.theme_loader.get_foreground_color(scope)
        bold, italic = self.theme_loader.get_font_style(scope)
        
        # Choose appropriate font
        font = self.base_font
        if bold and italic:
            font = self.bold_italic_font
        elif bold:
            font = self.bold_font
        elif italic:
            font = self.italic_font
        
        self.text_widget.tag_configure(tag_name, foreground=foreground, font=font)
        self.configured_tags.add(tag_name)
    
    def clear_syntax_tags(self):
        """Clear all syntax highlighting tags"""
        for tag in self.configured_tags:
            self.text_widget.tag_remove(tag, "1.0", tk.END)
    
    def highlight_text(self, file_path: str = None):
        """Apply syntax highlighting to the entire text"""
        if not PYGMENTS_AVAILABLE or not self.is_python_file(file_path):
            return
        
        # Get text content
        content = self.text_widget.get("1.0", tk.END)
        if not content.strip():
            return
        
        # Clear existing syntax tags
        self.clear_syntax_tags()
        
        try:
            # Tokenize the content
            tokens = list(self.lexer.get_tokens(content))
            
            # Apply highlighting
            self._apply_token_highlighting(tokens)
            
        except Exception as e:
            print(f"Error during syntax highlighting: {e}")
    
    def _apply_token_highlighting(self, tokens: List[Tuple]):
        """Apply highlighting based on tokens"""
        line_num = 1
        col_num = 0
        
        for token_type, text in tokens:
            if not text:
                continue
                
            # Calculate positions
            start_pos = f"{line_num}.{col_num}"
            
            # Update position based on text content
            lines = text.split('\n')
            if len(lines) > 1:
                line_num += len(lines) - 1
                col_num = len(lines[-1])
            else:
                col_num += len(text)
            
            end_pos = f"{line_num}.{col_num}"
            
            # Get scope for this token type
            scope = self._get_scope_for_token(token_type)
            if scope:
                tag_name = f"syntax_{scope.replace('.', '_')}"
                self.configure_tag(tag_name, scope)
                self.text_widget.tag_add(tag_name, start_pos, end_pos)
    
    def _get_scope_for_token(self, token_type) -> Optional[str]:
        """Map Pygments token to VSCode scope"""
        # Direct mapping
        if token_type in self.token_scope_map:
            return self.token_scope_map[token_type]
        
        # Try parent token types
        for parent_type in token_type.split():
            if parent_type in self.token_scope_map:
                return self.token_scope_map[parent_type]
        
        return None
    
    def highlight_range(self, start: str, end: str, file_path: str = None):
        """Highlight a specific range of text (for incremental updates)"""
        if not PYGMENTS_AVAILABLE or not self.is_python_file(file_path):
            return
        
        # For now, just re-highlight the entire text
        # This could be optimized for better performance
        self.highlight_text(file_path)
